/**
 * StreamProcessor - Processes streaming responses from AI providers
 * Handles real-time parsing of streaming chat completions and tool calls
 */

export class StreamProcessor {
  constructor() {
    this.buffer = '';
    this.currentToolCall = null;
    this.toolCalls = [];
    this.metrics = {
      chunksProcessed: 0,
      totalBytes: 0,
      processingTime: 0,
      errors: 0,
      startTime: null
    };
  }

  /**
   * Process a streaming chunk from the AI provider
   */
  processChunk(chunk) {
    const startTime = performance.now();

    if (!this.metrics.startTime) {
      this.metrics.startTime = startTime;
    }

    const result = {
      content: null,
      tool_calls: null,
      finished: false,
      error: null
    };

    try {
      // Track chunk processing
      this.metrics.chunksProcessed++;

      // Estimate chunk size
      const chunkSize = JSON.stringify(chunk).length;
      this.metrics.totalBytes += chunkSize;

      // Handle different chunk formats based on provider
      const delta = this.extractDelta(chunk);

      if (!delta) {
        return result;
      }

      // Process content
      if (delta.content) {
        result.content = delta.content;
      }

      // Process tool calls
      if (delta.tool_calls) {
        const processedToolCalls = this.processToolCalls(delta.tool_calls);
        if (processedToolCalls.length > 0) {
          result.tool_calls = processedToolCalls;
        }
      }

      // Check if stream is finished
      if (chunk.choices && chunk.choices[0] && chunk.choices[0].finish_reason) {
        result.finished = true;
      }

    } catch (error) {
      this.metrics.errors++;
      result.error = error.message;
    } finally {
      // Track processing time
      this.metrics.processingTime += performance.now() - startTime;
    }

    return result;
  }

  /**
   * Extract delta from chunk based on provider format
   */
  extractDelta(chunk) {
    // OpenAI format
    if (chunk.choices && chunk.choices[0] && chunk.choices[0].delta) {
      return chunk.choices[0].delta;
    }

    // Ollama format
    if (chunk.message) {
      return chunk.message;
    }

    // Deepseek format (similar to OpenAI)
    if (chunk.choices && chunk.choices[0] && chunk.choices[0].delta) {
      return chunk.choices[0].delta;
    }

    // Raw delta format
    if (chunk.content || chunk.tool_calls) {
      return chunk;
    }

    return null;
  }

  /**
   * Process tool calls from streaming delta
   */
  processToolCalls(toolCallsArray) {
    const completedToolCalls = [];

    for (const toolCallDelta of toolCallsArray) {
      const index = toolCallDelta.index || 0;

      // Initialize tool call if it doesn't exist
      if (!this.toolCalls[index]) {
        this.toolCalls[index] = {
          id: toolCallDelta.id || this.generateToolCallId(),
          type: toolCallDelta.type || 'function',
          function: {
            name: '',
            arguments: ''
          }
        };
      }

      const toolCall = this.toolCalls[index];

      // Update tool call with delta
      if (toolCallDelta.id) {
        toolCall.id = toolCallDelta.id;
      }

      if (toolCallDelta.type) {
        toolCall.type = toolCallDelta.type;
      }

      if (toolCallDelta.function) {
        if (toolCallDelta.function.name) {
          toolCall.function.name += toolCallDelta.function.name;
        }

        if (toolCallDelta.function.arguments) {
          toolCall.function.arguments += toolCallDelta.function.arguments;
        }
      }

      // Check if tool call is complete
      if (this.isToolCallComplete(toolCall)) {
        try {
          // Validate JSON arguments
          JSON.parse(toolCall.function.arguments);
          completedToolCalls.push({ ...toolCall });
        } catch (error) {
          // Arguments not yet complete or invalid JSON
        }
      }
    }

    return completedToolCalls;
  }

  /**
   * Check if a tool call is complete
   */
  isToolCallComplete(toolCall) {
    return (
      toolCall.function.name &&
      toolCall.function.arguments &&
      this.isValidJSON(toolCall.function.arguments)
    );
  }

  /**
   * Check if string is valid JSON
   */
  isValidJSON(str) {
    try {
      JSON.parse(str);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Generate a unique tool call ID
   */
  generateToolCallId() {
    return `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Reset processor state
   */
  reset() {
    this.buffer = '';
    this.currentToolCall = null;
    this.toolCalls = [];
  }

  /**
   * Get accumulated tool calls
   */
  getAccumulatedToolCalls() {
    return this.toolCalls.filter(tc => tc && this.isToolCallComplete(tc));
  }

  /**
   * Process complete stream and return final result
   */
  async processCompleteStream(stream) {
    let content = '';
    const toolCalls = [];
    let finished = false;
    let error = null;

    try {
      for await (const chunk of stream) {
        const processed = this.processChunk(chunk);

        if (processed.error) {
          error = processed.error;
          break;
        }

        if (processed.content) {
          content += processed.content;
        }

        if (processed.tool_calls) {
          toolCalls.push(...processed.tool_calls);
        }

        if (processed.finished) {
          finished = true;
          break;
        }
      }
    } catch (streamError) {
      error = streamError.message;
    }

    return {
      content: content.trim(),
      tool_calls: toolCalls,
      finished,
      error
    };
  }

  /**
   * Handle different streaming formats
   */
  handleProviderSpecificFormat(chunk, providerType) {
    switch (providerType) {
      case 'openai':
      case 'deepseek':
      case 'azure':
        return this.handleOpenAIFormat(chunk);
        
      case 'ollama':
        return this.handleOllamaFormat(chunk);
        
      default:
        return this.handleOpenAIFormat(chunk); // Default to OpenAI format
    }
  }

  /**
   * Handle OpenAI streaming format
   */
  handleOpenAIFormat(chunk) {
    if (chunk.choices && chunk.choices[0]) {
      const choice = chunk.choices[0];
      
      return {
        delta: choice.delta,
        finished: choice.finish_reason !== null,
        finishReason: choice.finish_reason
      };
    }
    
    return null;
  }

  /**
   * Handle Ollama streaming format
   */
  handleOllamaFormat(chunk) {
    return {
      delta: {
        content: chunk.response || '',
        tool_calls: chunk.tool_calls || null
      },
      finished: chunk.done || false,
      finishReason: chunk.done ? 'stop' : null
    };
  }

  /**
   * Validate streaming response
   */
  validateStreamingResponse(response) {
    const issues = [];

    if (!response.content && (!response.tool_calls || response.tool_calls.length === 0)) {
      issues.push('Response contains no content or tool calls');
    }

    if (response.tool_calls) {
      for (const toolCall of response.tool_calls) {
        if (!toolCall.function || !toolCall.function.name) {
          issues.push('Tool call missing function name');
        }

        if (!toolCall.function.arguments) {
          issues.push('Tool call missing arguments');
        } else {
          try {
            JSON.parse(toolCall.function.arguments);
          } catch {
            issues.push('Tool call arguments are not valid JSON');
          }
        }
      }
    }

    return {
      valid: issues.length === 0,
      issues
    };
  }

  /**
   * Extract metadata from streaming response
   */
  extractMetadata(chunk) {
    const metadata = {};

    // Usage information
    if (chunk.usage) {
      metadata.usage = chunk.usage;
    }

    // Model information
    if (chunk.model) {
      metadata.model = chunk.model;
    }

    // Provider-specific metadata
    if (chunk.system_fingerprint) {
      metadata.systemFingerprint = chunk.system_fingerprint;
    }

    // Timing information
    if (chunk.created) {
      metadata.created = chunk.created;
    }

    return metadata;
  }

  /**
   * Handle streaming errors
   */
  handleStreamingError(error) {
    const errorInfo = {
      type: 'streaming_error',
      message: error.message,
      recoverable: false
    };

    // Categorize error types
    if (error.message.includes('timeout')) {
      errorInfo.type = 'timeout';
      errorInfo.recoverable = true;
    } else if (error.message.includes('rate limit')) {
      errorInfo.type = 'rate_limit';
      errorInfo.recoverable = true;
    } else if (error.message.includes('network')) {
      errorInfo.type = 'network';
      errorInfo.recoverable = true;
    } else if (error.message.includes('authentication')) {
      errorInfo.type = 'authentication';
      errorInfo.recoverable = false;
    }

    return errorInfo;
  }

  /**
   * Reset processor state
   */
  reset() {
    this.buffer = '';
    this.currentToolCall = null;
    this.toolCalls = [];
    this.metrics = {
      chunksProcessed: 0,
      totalBytes: 0,
      processingTime: 0,
      errors: 0,
      startTime: null
    };
  }

  /**
   * Get performance metrics
   */
  getMetrics() {
    const totalTime = this.metrics.startTime ?
      performance.now() - this.metrics.startTime : 0;

    return {
      chunksProcessed: this.metrics.chunksProcessed,
      totalBytes: this.metrics.totalBytes,
      processingTime: this.metrics.processingTime,
      errors: this.metrics.errors,
      totalTime,
      averageChunkSize: this.metrics.chunksProcessed > 0 ?
        this.metrics.totalBytes / this.metrics.chunksProcessed : 0,
      averageProcessingTime: this.metrics.chunksProcessed > 0 ?
        this.metrics.processingTime / this.metrics.chunksProcessed : 0,
      throughput: totalTime > 0 ?
        this.metrics.totalBytes / (totalTime / 1000) : 0, // bytes per second
      errorRate: this.metrics.chunksProcessed > 0 ?
        this.metrics.errors / this.metrics.chunksProcessed : 0
    };
  }

  /**
   * Get formatted metrics for display
   */
  getFormattedMetrics() {
    const metrics = this.getMetrics();

    return {
      'Chunks Processed': metrics.chunksProcessed.toLocaleString(),
      'Total Bytes': this.formatBytes(metrics.totalBytes),
      'Processing Time': `${metrics.processingTime.toFixed(2)}ms`,
      'Total Time': `${metrics.totalTime.toFixed(2)}ms`,
      'Average Chunk Size': this.formatBytes(metrics.averageChunkSize),
      'Average Processing Time': `${metrics.averageProcessingTime.toFixed(2)}ms`,
      'Throughput': `${this.formatBytes(metrics.throughput)}/s`,
      'Error Rate': `${(metrics.errorRate * 100).toFixed(2)}%`
    };
  }

  /**
   * Format bytes for human readability
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  }
}
