/**
 * WorkflowManager - Advanced workflow management for chaining tools and operations
 * Enables complex multi-step operations with conditional logic and error recovery
 */

import { nanoid } from 'nanoid';

export class WorkflowManager {
  constructor(toolRegistry, approvalSystem, ui) {
    this.toolRegistry = toolRegistry;
    this.approvalSystem = approvalSystem;
    this.ui = ui;
    
    this.workflows = new Map();
    this.activeWorkflows = new Map();
    this.workflowHistory = [];
  }

  /**
   * Create a new workflow
   */
  createWorkflow(name, steps, options = {}) {
    const workflow = {
      id: nanoid(),
      name,
      steps,
      options: {
        continueOnError: options.continueOnError || false,
        maxRetries: options.maxRetries || 3,
        timeout: options.timeout || 300000, // 5 minutes
        parallel: options.parallel || false,
        ...options
      },
      created: new Date().toISOString(),
      status: 'created'
    };
    
    this.workflows.set(workflow.id, workflow);
    return workflow;
  }

  /**
   * Execute a workflow
   */
  async executeWorkflow(workflowId, context = {}) {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) {
      throw new Error(`Workflow ${workflowId} not found`);
    }

    const execution = {
      id: nanoid(),
      workflowId,
      startTime: Date.now(),
      endTime: null,
      status: 'running',
      results: [],
      errors: [],
      context: { ...context }
    };

    this.activeWorkflows.set(execution.id, execution);
    workflow.status = 'running';

    try {
      this.ui.info(`Starting workflow: ${workflow.name}`);
      
      if (workflow.options.parallel) {
        await this.executeStepsParallel(workflow, execution);
      } else {
        await this.executeStepsSequential(workflow, execution);
      }

      execution.status = 'completed';
      workflow.status = 'completed';
      this.ui.success(`Workflow completed: ${workflow.name}`);

    } catch (error) {
      execution.status = 'failed';
      workflow.status = 'failed';
      execution.errors.push({
        message: error.message,
        timestamp: Date.now()
      });
      
      this.ui.error(`Workflow failed: ${workflow.name} - ${error.message}`);
      throw error;
      
    } finally {
      execution.endTime = Date.now();
      this.activeWorkflows.delete(execution.id);
      this.workflowHistory.push(execution);
      
      // Keep only last 100 executions
      if (this.workflowHistory.length > 100) {
        this.workflowHistory = this.workflowHistory.slice(-100);
      }
    }

    return execution;
  }

  /**
   * Execute workflow steps sequentially
   */
  async executeStepsSequential(workflow, execution) {
    for (let i = 0; i < workflow.steps.length; i++) {
      const step = workflow.steps[i];
      
      try {
        this.ui.info(`Executing step ${i + 1}/${workflow.steps.length}: ${step.name || step.tool}`);
        
        const result = await this.executeStep(step, execution.context);
        
        execution.results.push({
          stepIndex: i,
          stepName: step.name || step.tool,
          result,
          timestamp: Date.now()
        });
        
        // Update context with step results if specified
        if (step.outputTo) {
          execution.context[step.outputTo] = result;
        }
        
      } catch (error) {
        const stepError = {
          stepIndex: i,
          stepName: step.name || step.tool,
          error: error.message,
          timestamp: Date.now()
        };
        
        execution.errors.push(stepError);
        
        if (!workflow.options.continueOnError) {
          throw error;
        }
        
        this.ui.warning(`Step failed but continuing: ${error.message}`);
      }
    }
  }

  /**
   * Execute workflow steps in parallel
   */
  async executeStepsParallel(workflow, execution) {
    const promises = workflow.steps.map(async (step, index) => {
      try {
        this.ui.info(`Starting parallel step: ${step.name || step.tool}`);
        
        const result = await this.executeStep(step, execution.context);
        
        return {
          stepIndex: index,
          stepName: step.name || step.tool,
          result,
          timestamp: Date.now()
        };
        
      } catch (error) {
        const stepError = {
          stepIndex: index,
          stepName: step.name || step.tool,
          error: error.message,
          timestamp: Date.now()
        };
        
        execution.errors.push(stepError);
        
        if (!workflow.options.continueOnError) {
          throw error;
        }
        
        return stepError;
      }
    });

    const results = await Promise.allSettled(promises);
    
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        execution.results.push(result.value);
      } else {
        execution.errors.push({
          stepIndex: index,
          stepName: workflow.steps[index].name || workflow.steps[index].tool,
          error: result.reason.message,
          timestamp: Date.now()
        });
      }
    });
  }

  /**
   * Execute a single workflow step
   */
  async executeStep(step, context) {
    // Resolve dynamic parameters from context
    const resolvedParams = this.resolveParameters(step.parameters || {}, context);
    
    // Check conditions if specified
    if (step.condition && !this.evaluateCondition(step.condition, context)) {
      this.ui.info(`Skipping step due to condition: ${step.name || step.tool}`);
      return { skipped: true, reason: 'condition not met' };
    }
    
    // Get the tool
    const tool = this.toolRegistry.getTool(step.tool);
    if (!tool) {
      throw new Error(`Tool not found: ${step.tool}`);
    }
    
    // Execute with approval if required
    if (step.requiresApproval !== false) {
      const approved = await this.approvalSystem.requestApproval({
        tool: step.tool,
        operation: resolvedParams.operation || 'execute',
        parameters: resolvedParams,
        riskLevel: step.riskLevel || 'medium'
      });
      
      if (!approved) {
        throw new Error(`Step not approved: ${step.name || step.tool}`);
      }
    }
    
    // Execute the tool
    return await tool.execute(resolvedParams);
  }

  /**
   * Resolve parameters with context variables
   */
  resolveParameters(parameters, context) {
    const resolved = {};
    
    for (const [key, value] of Object.entries(parameters)) {
      if (typeof value === 'string' && value.startsWith('${') && value.endsWith('}')) {
        // Extract variable name
        const varName = value.slice(2, -1);
        resolved[key] = context[varName] || value;
      } else {
        resolved[key] = value;
      }
    }
    
    return resolved;
  }

  /**
   * Evaluate a condition
   */
  evaluateCondition(condition, context) {
    try {
      // Simple condition evaluation
      // In a production system, you'd want a more sophisticated expression evaluator
      if (condition.type === 'exists') {
        return context[condition.variable] !== undefined;
      } else if (condition.type === 'equals') {
        return context[condition.variable] === condition.value;
      } else if (condition.type === 'not_equals') {
        return context[condition.variable] !== condition.value;
      }
      
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get workflow status
   */
  getWorkflowStatus(workflowId) {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) {
      return null;
    }
    
    const activeExecution = Array.from(this.activeWorkflows.values())
      .find(exec => exec.workflowId === workflowId);
    
    return {
      workflow,
      activeExecution,
      isRunning: !!activeExecution
    };
  }

  /**
   * List all workflows
   */
  listWorkflows() {
    return Array.from(this.workflows.values()).map(workflow => ({
      id: workflow.id,
      name: workflow.name,
      status: workflow.status,
      stepCount: workflow.steps.length,
      created: workflow.created
    }));
  }

  /**
   * Get workflow execution history
   */
  getExecutionHistory(workflowId = null) {
    if (workflowId) {
      return this.workflowHistory.filter(exec => exec.workflowId === workflowId);
    }
    return this.workflowHistory;
  }

  /**
   * Cancel a running workflow
   */
  async cancelWorkflow(executionId) {
    const execution = this.activeWorkflows.get(executionId);
    if (!execution) {
      throw new Error(`Execution ${executionId} not found or not running`);
    }
    
    execution.status = 'cancelled';
    execution.endTime = Date.now();
    
    const workflow = this.workflows.get(execution.workflowId);
    if (workflow) {
      workflow.status = 'cancelled';
    }
    
    this.activeWorkflows.delete(executionId);
    this.workflowHistory.push(execution);
    
    this.ui.warning(`Workflow cancelled: ${workflow?.name || executionId}`);
  }

  /**
   * Create a predefined workflow for common tasks
   */
  createPredefinedWorkflow(type, options = {}) {
    switch (type) {
      case 'project_setup':
        return this.createWorkflow('Project Setup', [
          {
            tool: 'analyze_context',
            name: 'Analyze Project',
            parameters: { deep: true },
            outputTo: 'projectInfo'
          },
          {
            tool: 'file_operations',
            name: 'Create README',
            parameters: {
              operation: 'create',
              path: 'README.md',
              content: '# ${projectInfo.projectType} Project\n\nGenerated by Kritrima AI CLI'
            },
            condition: { type: 'not_exists', variable: 'readme' }
          }
        ], options);
        
      case 'backup_project':
        return this.createWorkflow('Backup Project', [
          {
            tool: 'shell',
            name: 'Create Backup Directory',
            parameters: { command: 'mkdir -p backups' }
          },
          {
            tool: 'shell',
            name: 'Archive Project',
            parameters: { 
              command: `tar -czf backups/backup-${Date.now()}.tar.gz --exclude=node_modules --exclude=.git .`
            }
          }
        ], options);
        
      default:
        throw new Error(`Unknown predefined workflow type: ${type}`);
    }
  }
}
