/**
 * TerminalUI - Terminal user interface for the CLI application
 * Handles user interaction, display formatting, and visual feedback
 */

import chalk from 'chalk';
import ora from 'ora';
import inquirer from 'inquirer';

export class TerminalUI {
  constructor(config = {}) {
    this.config = {
      theme: config.theme || 'default',
      showTimestamps: config.showTimestamps !== false,
      showRiskLevels: config.showRiskLevels !== false,
      ...config
    };
    
    this.currentSpinner = null;
    this.currentAIResponse = '';
    this.isStreamingResponse = false;
  }

  /**
   * Show welcome message
   */
  showWelcome() {
    console.log(chalk.cyan.bold('\n🤖 Kritrima AI CLI'));
    console.log(chalk.gray('Production-ready agentic AI-powered CLI tool system\n'));
    console.log(chalk.yellow('Features:'));
    console.log(chalk.gray('• Autonomous shell command execution'));
    console.log(chalk.gray('• Comprehensive file operations'));
    console.log(chalk.gray('• Intelligent context awareness'));
    console.log(chalk.gray('• Multi-provider AI support (OpenAI, Deepseek, Ollama)'));
    console.log(chalk.gray('• Advanced safety controls\n'));
  }

  /**
   * Get user input
   */
  async getUserInput(prompt = '> ') {
    const answer = await inquirer.prompt([
      {
        type: 'input',
        name: 'input',
        message: chalk.cyan(prompt),
        validate: (input) => input.trim().length > 0 || 'Please enter a message'
      }
    ]);
    
    return answer.input.trim();
  }

  /**
   * Display success message
   */
  success(message) {
    const timestamp = this.config.showTimestamps ? this.getTimestamp() : '';
    console.log(chalk.green(`✅ ${timestamp}${message}`));
  }

  /**
   * Display error message
   */
  error(message) {
    const timestamp = this.config.showTimestamps ? this.getTimestamp() : '';
    console.log(chalk.red(`❌ ${timestamp}${message}`));
  }

  /**
   * Display warning message
   */
  warning(message) {
    const timestamp = this.config.showTimestamps ? this.getTimestamp() : '';
    console.log(chalk.yellow(`⚠️  ${timestamp}${message}`));
  }

  /**
   * Display info message
   */
  info(message) {
    const timestamp = this.config.showTimestamps ? this.getTimestamp() : '';
    console.log(chalk.blue(`ℹ️  ${timestamp}${message}`));
  }

  /**
   * Start spinner with message
   */
  spinner(message) {
    if (this.currentSpinner) {
      this.currentSpinner.stop();
    }
    this.currentSpinner = ora(message).start();
  }

  /**
   * Stop spinner
   */
  stopSpinner() {
    if (this.currentSpinner) {
      this.currentSpinner.stop();
      this.currentSpinner = null;
    }
  }

  /**
   * Start AI response streaming
   */
  startAIResponse() {
    this.isStreamingResponse = true;
    this.currentAIResponse = '';
    
    const timestamp = this.config.showTimestamps ? this.getTimestamp() : '';
    process.stdout.write(chalk.magenta(`🤖 ${timestamp}AI: `));
  }

  /**
   * Append to AI response
   */
  appendToAIResponse(content) {
    if (this.isStreamingResponse) {
      process.stdout.write(content);
      this.currentAIResponse += content;
    }
  }

  /**
   * End AI response streaming
   */
  endAIResponse() {
    if (this.isStreamingResponse) {
      process.stdout.write('\n\n');
      this.isStreamingResponse = false;
    }
  }

  /**
   * Display tool execution result
   */
  displayToolResult(toolName, result) {
    console.log(chalk.cyan(`\n🔧 Tool: ${toolName}`));
    
    if (result.success) {
      console.log(chalk.green('✅ Success'));
      
      if (result.stdout) {
        console.log(chalk.gray('Output:'));
        console.log(result.stdout);
      }
      
      if (result.stderr) {
        console.log(chalk.yellow('Warnings:'));
        console.log(result.stderr);
      }
    } else {
      console.log(chalk.red('❌ Failed'));
      console.log(chalk.red(result.error || 'Unknown error'));
    }
    
    console.log(''); // Empty line for spacing
  }

  /**
   * Display configuration
   */
  displayConfig(config) {
    console.log(chalk.cyan.bold('\n📋 Current Configuration:\n'));
    
    console.log(chalk.yellow('Provider:'));
    console.log(`  Type: ${config.provider.type}`);
    console.log(`  Model: ${config.provider.model}`);
    console.log(`  API Key: ${config.provider.apiKey ? '***' + config.provider.apiKey.slice(-4) : 'Not set'}`);
    
    console.log(chalk.yellow('\nSafety:'));
    console.log(`  Mode: ${config.safety.mode}`);
    console.log(`  Risk Threshold: ${config.safety.riskThreshold}`);
    
    console.log(chalk.yellow('\nTools:'));
    console.log(`  Shell Timeout: ${config.tools.shell.timeout}ms`);
    console.log(`  Max File Size: ${(config.tools.file.maxFileSize / 1024 / 1024).toFixed(1)}MB`);
    
    console.log('');
  }

  /**
   * Display sessions list
   */
  displaySessions(sessions) {
    if (sessions.length === 0) {
      console.log(chalk.gray('No sessions found.'));
      return;
    }
    
    console.log(chalk.cyan.bold('\n💬 Sessions:\n'));
    
    sessions.forEach((session, index) => {
      const createdAt = new Date(session.createdAt).toLocaleString();
      const updatedAt = new Date(session.updatedAt).toLocaleString();
      
      console.log(chalk.yellow(`${index + 1}. ${session.id}`));
      console.log(`   Created: ${createdAt}`);
      console.log(`   Updated: ${updatedAt}`);
      console.log(`   Messages: ${session.messageCount}`);
      console.log(`   Directory: ${session.workingDirectory}`);
      console.log('');
    });
  }

  /**
   * Display session details
   */
  displaySession(session) {
    console.log(chalk.cyan.bold(`\n💬 Session: ${session.id}\n`));
    
    console.log(chalk.yellow('Metadata:'));
    console.log(`  Created: ${new Date(session.createdAt).toLocaleString()}`);
    console.log(`  Updated: ${new Date(session.updatedAt).toLocaleString()}`);
    console.log(`  Messages: ${session.messages.length}`);
    console.log(`  Working Directory: ${session.metadata.workingDirectory}`);
    console.log(`  Platform: ${session.metadata.platform}`);
    
    console.log(chalk.yellow('\nStatistics:'));
    console.log(`  Tool Calls: ${session.stats.toolCallsExecuted || 0}`);
    console.log(`  Total Tokens: ${session.stats.totalTokens || 0}`);
    console.log(`  Execution Time: ${session.stats.executionTime || 0}ms`);
    
    if (session.messages.length > 0) {
      console.log(chalk.yellow('\nRecent Messages:'));
      const recentMessages = session.messages.slice(-5);
      
      recentMessages.forEach((message, index) => {
        const timestamp = new Date(message.timestamp).toLocaleString();
        const role = message.role.charAt(0).toUpperCase() + message.role.slice(1);
        
        console.log(`  ${index + 1}. [${timestamp}] ${role}: ${message.content.substring(0, 100)}${message.content.length > 100 ? '...' : ''}`);
      });
    }
    
    console.log('');
  }

  /**
   * Display context analysis
   */
  displayAnalysis(analysis, format = 'text') {
    switch (format) {
      case 'json':
        console.log(JSON.stringify(analysis, null, 2));
        break;
        
      case 'yaml':
        // Would need yaml library
        console.log('YAML format not implemented yet');
        break;
        
      case 'text':
      default:
        console.log(chalk.cyan.bold('\n🔍 Project Analysis:\n'));
        
        console.log(chalk.yellow('Overview:'));
        console.log(`  Working Directory: ${analysis.workingDirectory}`);
        console.log(`  Project Type: ${analysis.projectType || 'Unknown'}`);
        console.log(`  Files: ${analysis.files.length}`);
        console.log(`  Dependencies: ${analysis.dependencies.length}`);
        
        if (analysis.files.length > 0) {
          console.log(chalk.yellow('\nFile Types:'));
          const fileTypes = {};
          analysis.files.forEach(file => {
            const ext = file.path.split('.').pop() || 'no-extension';
            fileTypes[ext] = (fileTypes[ext] || 0) + 1;
          });
          
          Object.entries(fileTypes)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10)
            .forEach(([ext, count]) => {
              console.log(`  .${ext}: ${count} files`);
            });
        }
        
        if (analysis.dependencies.length > 0) {
          console.log(chalk.yellow('\nDependencies:'));
          analysis.dependencies.slice(0, 10).forEach(dep => {
            console.log(`  - ${dep}`);
          });
          
          if (analysis.dependencies.length > 10) {
            console.log(`  ... and ${analysis.dependencies.length - 10} more`);
          }
        }
        
        console.log('');
        break;
    }
  }

  /**
   * Display health check results
   */
  displayHealth(health) {
    console.log(chalk.cyan.bold('\n🏥 System Health Check:\n'));
    
    const statusColor = health.status === 'healthy' ? chalk.green : 
                       health.status === 'degraded' ? chalk.yellow : chalk.red;
    
    console.log(`Overall Status: ${statusColor(health.status.toUpperCase())}`);
    console.log(`Timestamp: ${new Date(health.timestamp).toLocaleString()}`);
    
    if (health.checks) {
      console.log(chalk.yellow('\nComponent Status:'));
      
      Object.entries(health.checks).forEach(([component, status]) => {
        const componentStatus = status.status === 'healthy' ? chalk.green('✅') : chalk.red('❌');
        console.log(`  ${componentStatus} ${component}: ${status.status}`);
        
        if (status.error) {
          console.log(chalk.red(`    Error: ${status.error}`));
        }
      });
    }
    
    console.log('');
  }

  /**
   * Display available tools
   */
  displayTools(tools) {
    console.log(chalk.cyan.bold('\n🔧 Available Tools:\n'));
    
    tools.forEach((tool, index) => {
      console.log(chalk.yellow(`${index + 1}. ${tool.name}`));
      console.log(`   Description: ${tool.description}`);
      console.log(`   Capabilities: ${tool.capabilities.join(', ')}`);
      console.log('');
    });
  }

  /**
   * Display context information
   */
  displayContext(context) {
    if (!context) {
      console.log(chalk.gray('No context available.'));
      return;
    }
    
    console.log(chalk.cyan.bold('\n📁 Current Context:\n'));
    
    console.log(chalk.yellow('Project Information:'));
    console.log(`  Directory: ${context.workingDirectory}`);
    console.log(`  Type: ${context.projectType || 'Unknown'}`);
    console.log(`  Files: ${context.files.length}`);
    
    if (context.dependencies && context.dependencies.length > 0) {
      console.log(chalk.yellow('\nDependencies:'));
      context.dependencies.slice(0, 5).forEach(dep => {
        console.log(`  - ${dep}`);
      });
      
      if (context.dependencies.length > 5) {
        console.log(`  ... and ${context.dependencies.length - 5} more`);
      }
    }
    
    console.log('');
  }

  /**
   * Show help information
   */
  showHelp() {
    console.log(chalk.cyan.bold('\n📚 Available Commands:\n'));
    
    const commands = [
      { cmd: '/help', desc: 'Show this help message' },
      { cmd: '/exit, /quit', desc: 'Exit the application' },
      { cmd: '/clear', desc: 'Clear the screen' },
      { cmd: '/context', desc: 'Show current project context' },
      { cmd: '/tools', desc: 'List available tools' },
      { cmd: '/mode [mode]', desc: 'Get/set approval mode (suggest, auto-edit, full-auto)' },
      { cmd: '/session', desc: 'Show current session information' },
      { cmd: '/save', desc: 'Force save current session' }
    ];
    
    commands.forEach(({ cmd, desc }) => {
      console.log(chalk.yellow(cmd.padEnd(20)) + chalk.gray(desc));
    });
    
    console.log('');
  }

  /**
   * Clear screen
   */
  clear() {
    console.clear();
    this.showWelcome();
  }

  /**
   * Get formatted timestamp
   */
  getTimestamp() {
    return chalk.gray(`[${new Date().toLocaleTimeString()}] `);
  }

  /**
   * Display progress bar
   */
  displayProgress(current, total, message = '') {
    const percentage = Math.round((current / total) * 100);
    const barLength = 30;
    const filledLength = Math.round((barLength * current) / total);

    const bar = '█'.repeat(filledLength) + '░'.repeat(barLength - filledLength);

    process.stdout.write(`\r${message} [${bar}] ${percentage}% (${current}/${total})`);

    if (current === total) {
      process.stdout.write('\n');
    }
  }

  /**
   * Display configuration
   */
  displayConfig(config) {
    console.log(chalk.cyan('\n📋 Current Configuration:'));
    console.log(chalk.gray('─'.repeat(40)));

    console.log(chalk.yellow('Provider:'));
    console.log(`  Type: ${config.provider.type}`);
    console.log(`  Model: ${config.provider.model}`);

    console.log(chalk.yellow('\nSafety:'));
    console.log(`  Mode: ${config.safety.mode}`);
    console.log(`  Risk Threshold: ${config.safety.riskThreshold}`);

    console.log(chalk.yellow('\nTools:'));
    console.log(`  Shell Timeout: ${config.tools.shell?.timeout || 'default'}ms`);
    console.log(`  File Max Size: ${config.tools.file?.maxFileSize || 'default'} bytes`);

    console.log('');
  }

  /**
   * Display sessions list
   */
  displaySessions(sessions) {
    if (sessions.length === 0) {
      console.log(chalk.gray('No sessions found.'));
      return;
    }

    console.log(chalk.cyan('\n📚 Sessions:'));
    console.log(chalk.gray('─'.repeat(60)));

    sessions.forEach(session => {
      const date = new Date(session.createdAt).toLocaleDateString();
      const time = new Date(session.createdAt).toLocaleTimeString();

      console.log(chalk.yellow(`${session.id.slice(0, 8)}...`));
      console.log(`  Created: ${date} ${time}`);
      console.log(`  Messages: ${session.messageCount}`);
      console.log(`  Directory: ${session.workingDirectory}`);
      console.log('');
    });
  }

  /**
   * Display single session
   */
  displaySession(session) {
    console.log(chalk.cyan(`\n📖 Session: ${session.id}`));
    console.log(chalk.gray('─'.repeat(60)));

    console.log(`Created: ${new Date(session.createdAt).toLocaleString()}`);
    console.log(`Updated: ${new Date(session.updatedAt).toLocaleString()}`);
    console.log(`Messages: ${session.messages.length}`);
    console.log(`Working Directory: ${session.metadata.workingDirectory}`);

    if (session.stats) {
      console.log(chalk.yellow('\nStatistics:'));
      console.log(`  Tool Calls: ${session.stats.toolCallsExecuted}`);
      console.log(`  Total Tokens: ${session.stats.totalTokens}`);
      console.log(`  Execution Time: ${session.stats.executionTime}ms`);
    }

    console.log('');
  }

  /**
   * Display analysis results
   */
  displayAnalysis(analysis, format = 'text') {
    if (format === 'json') {
      console.log(JSON.stringify(analysis, null, 2));
      return;
    }

    console.log(chalk.cyan('\n🔍 Project Analysis:'));
    console.log(chalk.gray('─'.repeat(50)));

    console.log(`Project Type: ${analysis.projectType || 'Unknown'}`);
    console.log(`Working Directory: ${analysis.workingDirectory}`);
    console.log(`Files: ${analysis.files.length}`);
    console.log(`Dependencies: ${analysis.dependencies.length}`);

    if (analysis.packageManagers.length > 0) {
      console.log(`Package Managers: ${analysis.packageManagers.join(', ')}`);
    }

    if (analysis.stats) {
      console.log(chalk.yellow('\nStatistics:'));
      console.log(`  Total Size: ${this.formatBytes(analysis.stats.totalSize)}`);
      console.log(`  File Types: ${Object.keys(analysis.stats.fileTypes).length}`);
    }

    console.log('');
  }

  /**
   * Display health check results
   */
  displayHealth(health) {
    console.log(chalk.cyan('\n🏥 System Health Check:'));
    console.log(chalk.gray('─'.repeat(50)));

    const statusIcon = health.status === 'healthy' ? '✅' : '❌';
    console.log(`Overall Status: ${statusIcon} ${health.status}`);

    if (health.checks) {
      console.log(chalk.yellow('\nComponent Health:'));

      Object.entries(health.checks).forEach(([component, check]) => {
        const icon = check.status === 'healthy' ? '✅' : '❌';
        console.log(`  ${component}: ${icon} ${check.status}`);

        if (check.error) {
          console.log(chalk.red(`    Error: ${check.error}`));
        }
      });
    }

    console.log('');
  }

  /**
   * Format bytes to human readable format
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Display available tools
   */
  displayTools(tools) {
    console.log(chalk.cyan('\n🔧 Available Tools:'));
    console.log(chalk.gray('─'.repeat(50)));

    tools.forEach(tool => {
      console.log(chalk.yellow(`${tool.name}`));
      console.log(`  Description: ${tool.description}`);
      console.log(`  Capabilities: ${tool.capabilities.join(', ')}`);
      console.log('');
    });
  }

  /**
   * Display context information
   */
  displayContext(context) {
    if (!context) {
      console.log(chalk.gray('No context available'));
      return;
    }

    console.log(chalk.cyan('\n📁 Current Context:'));
    console.log(chalk.gray('─'.repeat(50)));

    console.log(`Working Directory: ${context.workingDirectory}`);
    console.log(`Project Type: ${context.projectType || 'Unknown'}`);
    console.log(`Files: ${context.files?.length || 0}`);
    console.log(`Dependencies: ${context.dependencies?.length || 0}`);
    console.log('');
  }

  /**
   * Display tool execution result
   */
  displayToolResult(toolName, result) {
    console.log(chalk.green(`\n✅ Tool: ${toolName}`));

    if (result.success === false) {
      console.log(chalk.red(`❌ Error: ${result.error}`));
      return;
    }

    if (typeof result === 'string') {
      console.log(result);
    } else if (result.stdout) {
      console.log(result.stdout);
    } else if (result.result) {
      console.log(JSON.stringify(result.result, null, 2));
    } else {
      console.log(JSON.stringify(result, null, 2));
    }
    console.log('');
  }

  /**
   * Show help information
   */
  showHelp() {
    console.log(chalk.cyan('\n📚 Kritrima AI CLI Help'));
    console.log(chalk.gray('─'.repeat(50)));

    console.log(chalk.yellow('Special Commands:'));
    console.log('  /help     - Show this help');
    console.log('  /exit     - Exit the application');
    console.log('  /clear    - Clear the screen');
    console.log('  /context  - Show current context');
    console.log('  /tools    - Show available tools');
    console.log('  /mode     - Show/set approval mode');
    console.log('  /session  - Show current session info');
    console.log('  /save     - Force save session');

    console.log(chalk.yellow('\nApproval Modes:'));
    console.log('  suggest   - Always ask for approval');
    console.log('  auto-edit - Auto-approve file operations');
    console.log('  full-auto - Auto-approve based on risk');

    console.log('');
  }

  /**
   * Display performance metrics
   */
  displayPerformanceMetrics(metrics) {
    console.log(chalk.cyan.bold('\n📊 Performance Metrics:'));
    console.log(chalk.gray('──────────────────────────────────────────────────'));

    // System metrics
    console.log(chalk.yellow('System:'));
    console.log(`  Uptime: ${metrics.uptime}`);
    console.log(`  Memory Used: ${metrics.memory.used} / ${metrics.memory.total}`);
    console.log(`  RSS: ${metrics.memory.rss}`);

    // Request metrics
    console.log(chalk.yellow('\nRequests:'));
    console.log(`  Total: ${metrics.requests.total}`);
    console.log(`  Error Rate: ${metrics.requests.errorRate}`);
    console.log(`  Average Response Time: ${metrics.requests.averageResponseTime}`);

    // Tool metrics
    console.log(chalk.yellow('\nTools:'));
    console.log(`  Total Executions: ${metrics.tools.totalExecutions}`);

    if (Object.keys(metrics.tools.toolBreakdown).length > 0) {
      console.log('  Breakdown:');
      for (const [toolName, toolMetrics] of Object.entries(metrics.tools.toolBreakdown)) {
        console.log(`    ${toolName}: ${toolMetrics.executions} executions, ${toolMetrics.averageTime} avg, ${toolMetrics.errorRate} errors`);
      }
    }

    // Provider metrics
    if (Object.keys(metrics.providers).length > 0) {
      console.log(chalk.yellow('\nProviders:'));
      for (const [providerName, providerMetrics] of Object.entries(metrics.providers)) {
        console.log(`  ${providerName}:`);
        console.log(`    Requests: ${providerMetrics.requests}`);
        console.log(`    Tokens: ${providerMetrics.totalTokens}`);
        console.log(`    Avg Response Time: ${providerMetrics.averageResponseTime}`);
        console.log(`    Error Rate: ${providerMetrics.errorRate}`);
      }
    }

    // Streaming metrics
    if (metrics.streaming && Object.keys(metrics.streaming).length > 0) {
      console.log(chalk.yellow('\nStreaming:'));
      for (const [key, value] of Object.entries(metrics.streaming)) {
        if (key !== 'lastUpdated') {
          console.log(`  ${key}: ${value}`);
        }
      }
    }

    console.log('');
  }
}
