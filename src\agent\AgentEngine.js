/**
 * AgentEngine - Core orchestration engine for the AI agent
 * Manages the conversation loop, tool execution, and context awareness
 */

import { ConversationLoop } from './ConversationLoop.js';
import { UniversalProvider } from '../providers/UniversalProvider.js';
import { ToolRegistry } from '../tools/ToolRegistry.js';
import { ApprovalSystem } from '../safety/ApprovalSystem.js';
import { ProjectAnalyzer } from '../context/ProjectAnalyzer.js';
import { StreamProcessor } from '../utils/StreamProcessor.js';
import { ErrorHandler } from '../utils/ErrorHandler.js';
import { PerformanceMonitor } from '../utils/PerformanceMonitor.js';
import { nanoid } from 'nanoid';

export class AgentEngine {
  constructor(config, sessionManager, ui) {
    this.config = config;
    this.sessionManager = sessionManager;
    this.ui = ui;
    this.errorHandler = new ErrorHandler();
    
    // Initialize core components
    this.provider = new UniversalProvider(config.provider);
    this.toolRegistry = new ToolRegistry(config.tools);
    this.approvalSystem = new ApprovalSystem(config.safety);
    this.projectAnalyzer = new ProjectAnalyzer();
    this.streamProcessor = new StreamProcessor();
    this.performanceMonitor = new PerformanceMonitor(config.performance);
    
    // Current session state
    this.currentSession = null;
    this.conversationLoop = null;
    this.context = null;
  }

  /**
   * Start an interactive chat session
   */
  async startInteractiveSession(options = {}) {
    try {
      this.ui.showWelcome();
      
      // Load or create session
      if (options.session) {
        this.currentSession = await this.sessionManager.getSession(options.session);
        this.ui.info(`Resumed session: ${options.session}`);
      } else {
        this.currentSession = await this.sessionManager.createSession();
        this.ui.info(`Started new session: ${this.currentSession.id}`);
      }

      // Initialize context if enabled
      if (!options.noContext) {
        this.ui.spinner('Analyzing project context...');
        this.context = await this.projectAnalyzer.analyze(process.cwd(), {
          deep: options.deep || false
        });
        this.ui.stopSpinner();
        this.ui.success(`Context loaded: ${this.context.files.length} files, ${this.context.dependencies.length} dependencies`);
      }

      // Initialize conversation loop
      this.conversationLoop = new ConversationLoop(
        this.provider,
        this.toolRegistry,
        this.approvalSystem,
        this.streamProcessor,
        this.ui
      );

      // Set approval mode
      this.approvalSystem.setMode(options.mode || 'suggest');

      // Start interactive loop
      await this.conversationLoop.start(this.currentSession, this.context);

    } catch (error) {
      this.errorHandler.handleError(error);
      throw error;
    }
  }

  /**
   * Process a single message
   */
  async processMessage(message, options = {}) {
    const startTime = performance.now();
    let success = true;

    try {
      // Load or create session
      if (options.session) {
        this.currentSession = await this.sessionManager.getSession(options.session);
      } else {
        this.currentSession = await this.sessionManager.createSession();
      }

      // Initialize context if enabled
      if (!options.noContext) {
        this.context = await this.projectAnalyzer.analyze(process.cwd());
      }

      // Initialize conversation loop
      this.conversationLoop = new ConversationLoop(
        this.provider,
        this.toolRegistry,
        this.approvalSystem,
        this.streamProcessor,
        this.ui
      );

      // Set approval mode
      this.approvalSystem.setMode(options.mode || 'suggest');

      // Process the message
      const response = await this.conversationLoop.processMessage(
        message,
        this.currentSession,
        this.context
      );

      // Save session
      await this.sessionManager.saveSession(this.currentSession);

      return response;

    } catch (error) {
      success = false;
      this.errorHandler.handleError(error);
      throw error;
    } finally {
      // Record performance metrics
      const duration = performance.now() - startTime;
      this.performanceMonitor.recordRequest(duration, success);
    }
  }

  /**
   * Analyze project context
   */
  async analyzeContext(options = {}) {
    try {
      this.ui.spinner('Analyzing project context...');
      
      const analysis = await this.projectAnalyzer.analyze(process.cwd(), {
        deep: options.deep || false,
        includeContent: options.includeContent || false
      });

      this.ui.stopSpinner();
      return analysis;

    } catch (error) {
      this.errorHandler.handleError(error);
      throw error;
    }
  }

  /**
   * Check system health
   */
  async checkHealth() {
    const health = {
      timestamp: new Date().toISOString(),
      status: 'healthy',
      checks: {}
    };

    try {
      // Check AI provider connection
      this.ui.spinner('Checking AI provider...');
      health.checks.provider = await this.provider.healthCheck();
      
      // Check tool registry
      health.checks.tools = await this.toolRegistry.healthCheck();
      
      // Check session storage
      health.checks.storage = await this.sessionManager.healthCheck();
      
      // Check project context
      health.checks.context = await this.projectAnalyzer.healthCheck();

      this.ui.stopSpinner();

      // Determine overall status
      const allHealthy = Object.values(health.checks).every(check => check.status === 'healthy');
      health.status = allHealthy ? 'healthy' : 'degraded';

      return health;

    } catch (error) {
      this.ui.stopSpinner();
      health.status = 'unhealthy';
      health.error = error.message;
      return health;
    }
  }

  /**
   * Get available tools for current context
   */
  getAvailableTools() {
    return this.toolRegistry.getAvailableTools(this.context);
  }

  /**
   * Update configuration
   */
  async updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    
    // Reinitialize components with new config
    this.provider = new UniversalProvider(this.config.provider);
    this.toolRegistry = new ToolRegistry(this.config.tools);
    this.approvalSystem = new ApprovalSystem(this.config.safety);
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    try {
      if (this.currentSession) {
        await this.sessionManager.saveSession(this.currentSession);
      }
      
      if (this.conversationLoop) {
        await this.conversationLoop.cleanup();
      }
      
      await this.provider.cleanup();
      await this.toolRegistry.cleanup();
      
    } catch (error) {
      this.errorHandler.handleError(error);
    }
  }
}
