/**
 * ProjectAnalyzer - Analyzes project structure and provides context
 * Discovers files, dependencies, project type, and generates intelligent insights
 */

import fs from 'fs-extra';
import path from 'path';
import { glob } from 'glob';
import ignore from 'ignore';

export class ProjectAnalyzer {
  constructor(config = {}) {
    this.config = {
      maxFiles: config.maxFiles || 1000,
      maxDepth: config.maxDepth || 10,
      includeContent: config.includeContent || false,
      ignorePatterns: config.ignorePatterns || [
        '**/node_modules/**',
        '**/.git/**',
        '**/*.log',
        '**/.DS_Store',
        '**/Thumbs.db',
        '**/*.tmp',
        '**/*.temp'
      ],
      ...config
    };
    
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Analyze project at given path
   */
  async analyze(projectPath, options = {}) {
    const resolvedPath = path.resolve(projectPath);
    const cacheKey = `${resolvedPath}:${JSON.stringify(options)}`;
    
    // Check cache unless force refresh
    if (!options.forceRefresh && this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      }
    }

    const analysis = {
      workingDirectory: resolvedPath,
      timestamp: new Date().toISOString(),
      projectType: null,
      files: [],
      directories: [],
      dependencies: [],
      packageManagers: [],
      gitInfo: null,
      stats: {
        totalFiles: 0,
        totalSize: 0,
        fileTypes: {},
        lastModified: null
      }
    };

    try {
      // Discover files and directories
      await this.discoverFiles(resolvedPath, analysis, options);
      
      // Analyze project type
      analysis.projectType = this.detectProjectType(analysis.files);
      
      // Extract dependencies
      analysis.dependencies = await this.extractDependencies(resolvedPath, analysis.files);
      
      // Detect package managers
      analysis.packageManagers = this.detectPackageManagers(analysis.files);
      
      // Get git information
      analysis.gitInfo = await this.getGitInfo(resolvedPath);
      
      // Calculate statistics
      this.calculateStats(analysis);
      
      // Cache the result
      this.cache.set(cacheKey, {
        data: analysis,
        timestamp: Date.now()
      });
      
      return analysis;

    } catch (error) {
      throw new Error(`Failed to analyze project: ${error.message}`);
    }
  }

  /**
   * Discover files and directories
   */
  async discoverFiles(projectPath, analysis, options = {}) {
    const maxFiles = options.maxFiles || this.config.maxFiles;
    const maxDepth = options.maxDepth || this.config.maxDepth;
    const includeContent = options.includeContent || this.config.includeContent;

    // Create ignore filter - only use basic patterns, not overly restrictive
    const basicIgnorePatterns = [
      '.git/**',
      '**/.git/**',
      '**/node_modules/**',
      '**/*.log',
      '**/.DS_Store',
      '**/Thumbs.db'
    ];

    const ig = ignore().add(basicIgnorePatterns);
    if (options.excludePatterns) {
      ig.add(options.excludePatterns);
    }

    // Add .gitignore patterns if exists (but don't let it block everything)
    const gitignorePath = path.join(projectPath, '.gitignore');
    if (await fs.pathExists(gitignorePath)) {
      try {
        const gitignoreContent = await fs.readFile(gitignorePath, 'utf8');
        // Only add non-empty, non-comment lines
        const validPatterns = gitignoreContent
          .split('\n')
          .map(line => line.trim())
          .filter(line => line && !line.startsWith('#') && line !== '*');
        if (validPatterns.length > 0) {
          ig.add(validPatterns);
        }
      } catch (error) {
        // Continue without gitignore if we can't read it
      }
    }

    const files = [];
    const directories = [];

    const walkDirectory = async (dirPath, currentDepth = 0) => {
      if (currentDepth > maxDepth || files.length >= maxFiles) {
        return;
      }

      try {
        const entries = await fs.readdir(dirPath, { withFileTypes: true });

        for (const entry of entries) {
          const fullPath = path.join(dirPath, entry.name);
          const relativePath = path.relative(projectPath, fullPath);

          // Skip if ignored (but be more permissive for root level files)
          if (currentDepth > 0 && ig.ignores(relativePath)) {
            continue;
          }

          // For root level, only ignore very specific patterns
          if (currentDepth === 0 && (
            entry.name === 'node_modules' ||
            entry.name === '.git' ||
            entry.name.endsWith('.log')
          )) {
            continue;
          }

          if (entry.isDirectory()) {
            directories.push({
              path: relativePath,
              name: entry.name,
              fullPath
            });

            await walkDirectory(fullPath, currentDepth + 1);
          } else if (entry.isFile()) {
            const fileInfo = await this.getFileInfo(fullPath, relativePath, includeContent);

            // Filter by file types if specified
            if (options.fileTypes && options.fileTypes.length > 0) {
              const ext = path.extname(relativePath).slice(1).toLowerCase();
              if (!options.fileTypes.includes(ext)) {
                continue;
              }
            }

            files.push(fileInfo);

            if (files.length >= maxFiles) {
              break;
            }
          }
        }
      } catch (error) {
        // Skip directories we can't read, but log for debugging
        console.error(`Could not read directory ${dirPath}:`, error.message);
      }
    };

    await walkDirectory(projectPath);
    
    analysis.files = files;
    analysis.directories = directories;
  }

  /**
   * Get file information
   */
  async getFileInfo(fullPath, relativePath, includeContent = false) {
    const stats = await fs.stat(fullPath);
    const ext = path.extname(relativePath).slice(1).toLowerCase();
    
    const fileInfo = {
      path: relativePath,
      name: path.basename(relativePath),
      extension: ext,
      size: stats.size,
      modified: stats.mtime.toISOString(),
      created: stats.birthtime.toISOString()
    };

    // Include content for small text files if requested
    if (includeContent && this.isTextFile(ext) && stats.size < 100 * 1024) { // 100KB limit
      try {
        fileInfo.content = await fs.readFile(fullPath, 'utf8');
        fileInfo.lines = fileInfo.content.split('\n').length;
      } catch (error) {
        // Skip files we can't read as text
      }
    }

    return fileInfo;
  }

  /**
   * Check if file is likely a text file
   */
  isTextFile(extension) {
    const textExtensions = [
      'js', 'ts', 'jsx', 'tsx', 'py', 'java', 'c', 'cpp', 'h', 'hpp',
      'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt', 'scala',
      'html', 'css', 'scss', 'sass', 'less', 'xml', 'json', 'yaml', 'yml',
      'md', 'txt', 'rst', 'tex', 'sql', 'sh', 'bash', 'zsh', 'fish',
      'dockerfile', 'makefile', 'cmake', 'gradle', 'properties', 'ini', 'cfg'
    ];
    
    return textExtensions.includes(extension.toLowerCase());
  }

  /**
   * Detect project type based on files
   */
  detectProjectType(files) {
    const filePaths = files.map(f => f.path.toLowerCase());
    
    // Node.js/JavaScript
    if (filePaths.some(f => f.includes('package.json'))) {
      if (filePaths.some(f => f.includes('next.config'))) return 'Next.js';
      if (filePaths.some(f => f.includes('nuxt.config'))) return 'Nuxt.js';
      if (filePaths.some(f => f.includes('vue.config'))) return 'Vue.js';
      if (filePaths.some(f => f.includes('angular.json'))) return 'Angular';
      if (filePaths.some(f => f.includes('svelte.config'))) return 'Svelte';
      return 'Node.js';
    }
    
    // Python
    if (filePaths.some(f => f.includes('requirements.txt') || f.includes('setup.py') || f.includes('pyproject.toml'))) {
      if (filePaths.some(f => f.includes('manage.py'))) return 'Django';
      if (filePaths.some(f => f.includes('app.py') || f.includes('wsgi.py'))) return 'Flask';
      return 'Python';
    }
    
    // Java
    if (filePaths.some(f => f.includes('pom.xml'))) return 'Maven';
    if (filePaths.some(f => f.includes('build.gradle'))) return 'Gradle';
    if (files.some(f => f.extension === 'java')) return 'Java';
    
    // .NET
    if (filePaths.some(f => f.endsWith('.csproj') || f.endsWith('.sln'))) return '.NET';
    
    // Rust
    if (filePaths.some(f => f.includes('cargo.toml'))) return 'Rust';
    
    // Go
    if (filePaths.some(f => f.includes('go.mod'))) return 'Go';
    
    // PHP
    if (filePaths.some(f => f.includes('composer.json'))) return 'PHP';
    
    // Ruby
    if (filePaths.some(f => f.includes('gemfile'))) return 'Ruby';
    
    // Docker
    if (filePaths.some(f => f.includes('dockerfile'))) return 'Docker';
    
    // Static site
    if (files.some(f => ['html', 'css', 'js'].includes(f.extension))) {
      return 'Static Website';
    }
    
    return 'Unknown';
  }

  /**
   * Extract dependencies from package files
   */
  async extractDependencies(projectPath, files) {
    const dependencies = [];
    
    // Node.js dependencies
    const packageJsonFile = files.find(f => f.path === 'package.json');
    if (packageJsonFile) {
      try {
        const packageJson = JSON.parse(await fs.readFile(path.join(projectPath, 'package.json'), 'utf8'));
        if (packageJson.dependencies) {
          dependencies.push(...Object.keys(packageJson.dependencies));
        }
        if (packageJson.devDependencies) {
          dependencies.push(...Object.keys(packageJson.devDependencies));
        }
      } catch (error) {
        // Skip invalid package.json
      }
    }
    
    // Python dependencies
    const requirementsFile = files.find(f => f.path === 'requirements.txt');
    if (requirementsFile) {
      try {
        const requirements = await fs.readFile(path.join(projectPath, 'requirements.txt'), 'utf8');
        const pythonDeps = requirements
          .split('\n')
          .map(line => line.trim())
          .filter(line => line && !line.startsWith('#'))
          .map(line => line.split('==')[0].split('>=')[0].split('<=')[0].trim());
        dependencies.push(...pythonDeps);
      } catch (error) {
        // Skip invalid requirements.txt
      }
    }
    
    // Rust dependencies
    const cargoFile = files.find(f => f.path === 'Cargo.toml');
    if (cargoFile) {
      try {
        const cargoContent = await fs.readFile(path.join(projectPath, 'Cargo.toml'), 'utf8');
        // Simple regex to extract dependency names (not a full TOML parser)
        const depMatches = cargoContent.match(/^\s*([a-zA-Z0-9_-]+)\s*=/gm);
        if (depMatches) {
          const rustDeps = depMatches.map(match => match.split('=')[0].trim());
          dependencies.push(...rustDeps);
        }
      } catch (error) {
        // Skip invalid Cargo.toml
      }
    }
    
    return [...new Set(dependencies)]; // Remove duplicates
  }

  /**
   * Detect package managers
   */
  detectPackageManagers(files) {
    const managers = [];
    const filePaths = files.map(f => f.path.toLowerCase());
    
    if (filePaths.includes('package.json')) managers.push('npm');
    if (filePaths.includes('yarn.lock')) managers.push('yarn');
    if (filePaths.includes('pnpm-lock.yaml')) managers.push('pnpm');
    if (filePaths.includes('requirements.txt')) managers.push('pip');
    if (filePaths.includes('pipfile')) managers.push('pipenv');
    if (filePaths.includes('poetry.lock')) managers.push('poetry');
    if (filePaths.includes('cargo.toml')) managers.push('cargo');
    if (filePaths.includes('go.mod')) managers.push('go modules');
    if (filePaths.includes('composer.json')) managers.push('composer');
    if (filePaths.includes('gemfile')) managers.push('bundler');
    
    return managers;
  }

  /**
   * Get git information
   */
  async getGitInfo(projectPath) {
    const gitDir = path.join(projectPath, '.git');
    
    if (!await fs.pathExists(gitDir)) {
      return null;
    }

    const gitInfo = {
      isGitRepo: true,
      branch: null,
      remotes: [],
      lastCommit: null
    };

    try {
      // Get current branch
      const headPath = path.join(gitDir, 'HEAD');
      if (await fs.pathExists(headPath)) {
        const headContent = await fs.readFile(headPath, 'utf8');
        const branchMatch = headContent.match(/ref: refs\/heads\/(.+)/);
        if (branchMatch) {
          gitInfo.branch = branchMatch[1].trim();
        }
      }

      // Get remotes
      const configPath = path.join(gitDir, 'config');
      if (await fs.pathExists(configPath)) {
        const configContent = await fs.readFile(configPath, 'utf8');
        const remoteMatches = configContent.match(/\[remote "(.+)"\]/g);
        if (remoteMatches) {
          gitInfo.remotes = remoteMatches.map(match => 
            match.match(/\[remote "(.+)"\]/)[1]
          );
        }
      }
    } catch (error) {
      // Git info is optional, continue without it
    }

    return gitInfo;
  }

  /**
   * Calculate project statistics
   */
  calculateStats(analysis) {
    const stats = analysis.stats;
    
    stats.totalFiles = analysis.files.length;
    stats.totalSize = analysis.files.reduce((sum, file) => sum + file.size, 0);
    
    // File type distribution
    for (const file of analysis.files) {
      const ext = file.extension || 'no-extension';
      stats.fileTypes[ext] = (stats.fileTypes[ext] || 0) + 1;
    }
    
    // Last modified
    const modifiedDates = analysis.files
      .map(f => new Date(f.modified))
      .sort((a, b) => b - a);
    
    if (modifiedDates.length > 0) {
      stats.lastModified = modifiedDates[0].toISOString();
    }
  }

  /**
   * Health check
   */
  async healthCheck() {
    try {
      // Test basic file system access
      const testPath = process.cwd();
      await fs.access(testPath, fs.constants.R_OK);
      
      return {
        status: 'healthy',
        canReadFileSystem: true,
        cacheSize: this.cache.size
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message
      };
    }
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      entries: Array.from(this.cache.keys())
    };
  }
}
