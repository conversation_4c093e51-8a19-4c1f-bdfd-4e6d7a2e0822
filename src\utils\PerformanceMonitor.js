/**
 * PerformanceMonitor - System-wide performance monitoring and analytics
 * Tracks metrics across all components for optimization and debugging
 */

import fs from 'fs-extra';
import path from 'path';
import os from 'os';

export class PerformanceMonitor {
  constructor(config = {}) {
    this.config = {
      enabled: config.enabled !== false,
      sampleInterval: config.sampleInterval || 5000, // 5 seconds
      maxSamples: config.maxSamples || 1000,
      logFile: config.logFile || path.join(os.homedir(), '.kritrima', 'performance.log'),
      ...config
    };
    
    this.metrics = {
      system: {
        cpuUsage: [],
        memoryUsage: [],
        diskUsage: []
      },
      application: {
        requestCount: 0,
        responseTime: [],
        errorRate: 0,
        toolExecutions: 0,
        streamingMetrics: {}
      },
      providers: new Map(),
      tools: new Map(),
      sessions: new Map()
    };
    
    this.startTime = Date.now();
    this.samplingInterval = null;
    
    if (this.config.enabled) {
      this.startMonitoring();
    }
  }

  /**
   * Start performance monitoring
   */
  startMonitoring() {
    this.samplingInterval = setInterval(() => {
      this.collectSystemMetrics();
    }, this.config.sampleInterval);
    
    // Cleanup on process exit
    process.on('exit', () => this.stopMonitoring());
    process.on('SIGINT', () => this.stopMonitoring());
    process.on('SIGTERM', () => this.stopMonitoring());
  }

  /**
   * Stop performance monitoring
   */
  stopMonitoring() {
    if (this.samplingInterval) {
      clearInterval(this.samplingInterval);
      this.samplingInterval = null;
    }
  }

  /**
   * Collect system metrics
   */
  collectSystemMetrics() {
    try {
      // CPU usage
      const cpuUsage = process.cpuUsage();
      this.addMetric('system.cpuUsage', {
        user: cpuUsage.user,
        system: cpuUsage.system,
        timestamp: Date.now()
      });

      // Memory usage
      const memUsage = process.memoryUsage();
      this.addMetric('system.memoryUsage', {
        rss: memUsage.rss,
        heapTotal: memUsage.heapTotal,
        heapUsed: memUsage.heapUsed,
        external: memUsage.external,
        arrayBuffers: memUsage.arrayBuffers,
        timestamp: Date.now()
      });

      // System load
      const loadAvg = os.loadavg();
      this.addMetric('system.loadAverage', {
        load1: loadAvg[0],
        load5: loadAvg[1],
        load15: loadAvg[2],
        timestamp: Date.now()
      });

    } catch (error) {
      // Continue monitoring even if some metrics fail
    }
  }

  /**
   * Add a metric sample
   */
  addMetric(path, value) {
    const parts = path.split('.');
    let current = this.metrics;
    
    for (let i = 0; i < parts.length - 1; i++) {
      if (!current[parts[i]]) {
        current[parts[i]] = {};
      }
      current = current[parts[i]];
    }
    
    const key = parts[parts.length - 1];
    if (!Array.isArray(current[key])) {
      current[key] = [];
    }
    
    current[key].push(value);
    
    // Limit sample size
    if (current[key].length > this.config.maxSamples) {
      current[key] = current[key].slice(-this.config.maxSamples);
    }
  }

  /**
   * Record request metrics
   */
  recordRequest(duration, success = true) {
    this.metrics.application.requestCount++;
    this.addMetric('application.responseTime', {
      duration,
      success,
      timestamp: Date.now()
    });
    
    if (!success) {
      this.metrics.application.errorRate++;
    }
  }

  /**
   * Record tool execution
   */
  recordToolExecution(toolName, duration, success = true) {
    this.metrics.application.toolExecutions++;
    
    if (!this.metrics.tools.has(toolName)) {
      this.metrics.tools.set(toolName, {
        executions: 0,
        totalTime: 0,
        errors: 0,
        averageTime: 0
      });
    }
    
    const toolMetrics = this.metrics.tools.get(toolName);
    toolMetrics.executions++;
    toolMetrics.totalTime += duration;
    toolMetrics.averageTime = toolMetrics.totalTime / toolMetrics.executions;
    
    if (!success) {
      toolMetrics.errors++;
    }
  }

  /**
   * Record provider metrics
   */
  recordProviderMetrics(providerType, metrics) {
    if (!this.metrics.providers.has(providerType)) {
      this.metrics.providers.set(providerType, {
        requests: 0,
        totalTokens: 0,
        errors: 0,
        averageResponseTime: 0,
        totalResponseTime: 0
      });
    }
    
    const providerMetrics = this.metrics.providers.get(providerType);
    providerMetrics.requests++;
    
    if (metrics.tokens) {
      providerMetrics.totalTokens += metrics.tokens;
    }
    
    if (metrics.responseTime) {
      providerMetrics.totalResponseTime += metrics.responseTime;
      providerMetrics.averageResponseTime = 
        providerMetrics.totalResponseTime / providerMetrics.requests;
    }
    
    if (metrics.error) {
      providerMetrics.errors++;
    }
  }

  /**
   * Record streaming metrics
   */
  recordStreamingMetrics(metrics) {
    this.metrics.application.streamingMetrics = {
      ...this.metrics.application.streamingMetrics,
      ...metrics,
      lastUpdated: Date.now()
    };
  }

  /**
   * Get current performance summary
   */
  getPerformanceSummary() {
    const uptime = Date.now() - this.startTime;
    const currentMemory = process.memoryUsage();
    
    return {
      uptime: this.formatDuration(uptime),
      memory: {
        used: this.formatBytes(currentMemory.heapUsed),
        total: this.formatBytes(currentMemory.heapTotal),
        rss: this.formatBytes(currentMemory.rss)
      },
      requests: {
        total: this.metrics.application.requestCount,
        errorRate: this.metrics.application.requestCount > 0 ? 
          (this.metrics.application.errorRate / this.metrics.application.requestCount * 100).toFixed(2) + '%' : '0%',
        averageResponseTime: this.getAverageResponseTime()
      },
      tools: {
        totalExecutions: this.metrics.application.toolExecutions,
        toolBreakdown: this.getToolBreakdown()
      },
      providers: this.getProviderBreakdown(),
      streaming: this.metrics.application.streamingMetrics
    };
  }

  /**
   * Get average response time
   */
  getAverageResponseTime() {
    const responseTimes = this.metrics.application.responseTime;
    if (responseTimes.length === 0) return '0ms';
    
    const total = responseTimes.reduce((sum, rt) => sum + rt.duration, 0);
    return `${(total / responseTimes.length).toFixed(2)}ms`;
  }

  /**
   * Get tool execution breakdown
   */
  getToolBreakdown() {
    const breakdown = {};
    
    for (const [toolName, metrics] of this.metrics.tools) {
      breakdown[toolName] = {
        executions: metrics.executions,
        averageTime: `${metrics.averageTime.toFixed(2)}ms`,
        errorRate: metrics.executions > 0 ? 
          `${(metrics.errors / metrics.executions * 100).toFixed(2)}%` : '0%'
      };
    }
    
    return breakdown;
  }

  /**
   * Get provider performance breakdown
   */
  getProviderBreakdown() {
    const breakdown = {};
    
    for (const [providerType, metrics] of this.metrics.providers) {
      breakdown[providerType] = {
        requests: metrics.requests,
        totalTokens: metrics.totalTokens,
        averageResponseTime: `${metrics.averageResponseTime.toFixed(2)}ms`,
        errorRate: metrics.requests > 0 ? 
          `${(metrics.errors / metrics.requests * 100).toFixed(2)}%` : '0%'
      };
    }
    
    return breakdown;
  }

  /**
   * Format duration for display
   */
  formatDuration(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  /**
   * Format bytes for display
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  }

  /**
   * Export metrics to file
   */
  async exportMetrics() {
    try {
      await fs.ensureDir(path.dirname(this.config.logFile));
      
      const exportData = {
        timestamp: new Date().toISOString(),
        uptime: Date.now() - this.startTime,
        summary: this.getPerformanceSummary(),
        rawMetrics: {
          system: this.metrics.system,
          application: this.metrics.application,
          providers: Object.fromEntries(this.metrics.providers),
          tools: Object.fromEntries(this.metrics.tools)
        }
      };
      
      await fs.writeFile(
        this.config.logFile, 
        JSON.stringify(exportData, null, 2)
      );
      
      return this.config.logFile;
      
    } catch (error) {
      throw new Error(`Failed to export metrics: ${error.message}`);
    }
  }

  /**
   * Clear all metrics
   */
  clearMetrics() {
    this.metrics = {
      system: {
        cpuUsage: [],
        memoryUsage: [],
        diskUsage: []
      },
      application: {
        requestCount: 0,
        responseTime: [],
        errorRate: 0,
        toolExecutions: 0,
        streamingMetrics: {}
      },
      providers: new Map(),
      tools: new Map(),
      sessions: new Map()
    };
    
    this.startTime = Date.now();
  }
}
