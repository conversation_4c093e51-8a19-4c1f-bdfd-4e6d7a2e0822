#!/usr/bin/env node

/**
 * Kritrima AI CLI - Production-ready agentic AI-powered CLI tool system
 * Entry point for the application
 */

import { Command } from 'commander';
import chalk from 'chalk';
import { config } from 'dotenv';
import { AgentEngine } from './agent/AgentEngine.js';
import { ConfigManager } from './config/ConfigManager.js';
import { TerminalUI } from './ui/TerminalUI.js';
import { SessionManager } from './storage/SessionManager.js';
import { ErrorHandler } from './utils/ErrorHandler.js';

// Load environment variables
config();

const program = new Command();
const ui = new TerminalUI();
const errorHandler = new ErrorHandler();

// Global error handling
process.on('uncaughtException', (error) => {
  errorHandler.handleCriticalError(error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  errorHandler.handleUnhandledRejection(reason, promise);
  process.exit(1);
});

// CLI Configuration
program
  .name('kritrima')
  .description('AI-powered CLI tool for autonomous code execution and file operations')
  .version('1.0.0')
  .option('-v, --verbose', 'Enable verbose logging')
  .option('-c, --config <path>', 'Path to configuration file')
  .option('-p, --provider <provider>', 'AI provider (openai, deepseek, ollama)')
  .option('-m, --model <model>', 'AI model to use')
  .option('-s, --session <id>', 'Resume specific session')
  .option('--mode <mode>', 'Approval mode (suggest, auto-edit, full-auto)', 'suggest')
  .option('--no-context', 'Disable automatic context discovery')
  .option('--safe-mode', 'Enable maximum safety restrictions');

// Main chat command
program
  .command('chat [message]')
  .description('Start interactive chat or send a single message')
  .action(async (message, options) => {
    try {
      const config = await ConfigManager.load(program.opts().config);
      const sessionManager = new SessionManager();
      const agentEngine = new AgentEngine(config, sessionManager, ui);

      if (message) {
        // Single message mode
        await agentEngine.processMessage(message, program.opts());
      } else {
        // Interactive mode
        await agentEngine.startInteractiveSession(program.opts());
      }
    } catch (error) {
      errorHandler.handleError(error);
      process.exit(1);
    }
  });

// Configuration commands
program
  .command('config')
  .description('Manage configuration')
  .option('--init', 'Initialize configuration')
  .option('--show', 'Show current configuration')
  .option('--set <key=value>', 'Set configuration value')
  .action(async (options) => {
    try {
      const configManager = new ConfigManager();
      
      if (options.init) {
        await configManager.initialize();
        ui.success('Configuration initialized successfully');
      } else if (options.show) {
        const config = await configManager.load();
        ui.displayConfig(config);
      } else if (options.set) {
        const [key, value] = options.set.split('=');
        await configManager.set(key, value);
        ui.success(`Configuration updated: ${key} = ${value}`);
      } else {
        ui.error('Please specify a configuration action');
      }
    } catch (error) {
      errorHandler.handleError(error);
      process.exit(1);
    }
  });

// Session management commands
program
  .command('sessions')
  .description('Manage chat sessions')
  .option('--list', 'List all sessions')
  .option('--show <id>', 'Show session details')
  .option('--delete <id>', 'Delete a session')
  .option('--clear', 'Clear all sessions')
  .action(async (options) => {
    try {
      const sessionManager = new SessionManager();
      
      if (options.list) {
        const sessions = await sessionManager.listSessions();
        ui.displaySessions(sessions);
      } else if (options.show) {
        const session = await sessionManager.getSession(options.show);
        ui.displaySession(session);
      } else if (options.delete) {
        await sessionManager.deleteSession(options.delete);
        ui.success(`Session ${options.delete} deleted`);
      } else if (options.clear) {
        await sessionManager.clearAllSessions();
        ui.success('All sessions cleared');
      } else {
        const sessions = await sessionManager.listSessions();
        ui.displaySessions(sessions);
      }
    } catch (error) {
      errorHandler.handleError(error);
      process.exit(1);
    }
  });

// Context analysis command
program
  .command('analyze')
  .description('Analyze current project context')
  .option('--deep', 'Perform deep analysis')
  .option('--output <format>', 'Output format (json, yaml, text)', 'text')
  .action(async (options) => {
    try {
      const config = await ConfigManager.load();
      const agentEngine = new AgentEngine(config, new SessionManager(), ui);
      
      const analysis = await agentEngine.analyzeContext(options);
      ui.displayAnalysis(analysis, options.output);
    } catch (error) {
      errorHandler.handleError(error);
      process.exit(1);
    }
  });

// Health check command
program
  .command('health')
  .description('Check system health and configuration')
  .action(async () => {
    try {
      const config = await ConfigManager.load();
      const agentEngine = new AgentEngine(config, new SessionManager(), ui);

      const health = await agentEngine.checkHealth();
      ui.displayHealth(health);
    } catch (error) {
      errorHandler.handleError(error);
      process.exit(1);
    }
  });

// Performance monitoring command
program
  .command('performance')
  .description('View performance metrics and statistics')
  .option('--export', 'Export metrics to file')
  .option('--clear', 'Clear performance metrics')
  .action(async (options) => {
    try {
      const config = await ConfigManager.load();
      const agentEngine = new AgentEngine(config, new SessionManager(), ui);

      if (options.clear) {
        agentEngine.performanceMonitor.clearMetrics();
        ui.success('Performance metrics cleared');
      } else if (options.export) {
        const exportPath = await agentEngine.performanceMonitor.exportMetrics();
        ui.success(`Performance metrics exported to: ${exportPath}`);
      } else {
        const metrics = agentEngine.performanceMonitor.getPerformanceSummary();
        ui.displayPerformanceMetrics(metrics);
      }
    } catch (error) {
      errorHandler.handleError(error);
      process.exit(1);
    }
  });

// Default action - start interactive chat
program.action(async (options) => {
  try {
    const config = await ConfigManager.load(options.config);
    const sessionManager = new SessionManager();
    const agentEngine = new AgentEngine(config, sessionManager, ui);
    
    await agentEngine.startInteractiveSession(options);
  } catch (error) {
    errorHandler.handleError(error);
    process.exit(1);
  }
});

// Parse command line arguments
program.parse();

// If no command provided, show help
if (!process.argv.slice(2).length) {
  ui.showWelcome();
  program.outputHelp();
}
